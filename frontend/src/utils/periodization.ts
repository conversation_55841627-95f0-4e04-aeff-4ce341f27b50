import { Exercise } from "../types/api/exercises.types";

export type PeriodizationEntry = {
  week: number;
  assignmentDate: string;
  exerciseType: "MT exercise" | "MW exercise" | "visualization";
  exerciseName: string;
  exerciseId: string | number;
};

/**
 * Periodization algorithm:
 * Exercise ID structure:
 * [type: mt (mental toughness)/mw (mental wellness)]-[skill: (b1/b2/...)]-[difficulty: (0/1/2/3) exercise identifier: (a/b/c)]
 * So therefore, Exercise ID mt-b1-0a is a mental toughness exercise for skill b1, difficulty 0, exercise a
 *
 * Run validation on start and end dates:
 * - Minimum of 4 weeks for off season
 * - Minimum of 4 weeks for prep
 * - Minimum of 2 weeks for pre-comp
 *
 * Schedule mental toughness:
 * First, group all exercises into skill buckets sorted by difficulty and then alphabetically.
 * Mental toughness will have the following skills: B1, B2, B3, C1, C2, C3, C4, C5
 * For example:
 * B1_bucket: [{ id: "mt-b1-0a", difficulty: 0 }, {id: "mt-b1-0b", difficulty: 0 }, { id: "mt-b1-1a", difficulty: 1 }]
 * B2_bucket: [{ id: "mt-b2-0a", difficulty: 0 }, {id: "mt-b2-0b", difficulty: 0 }, { id: "mt-b2-1a", difficulty: 1 }]
 * B3_bucket: [{ id: "mt-b3-0a", difficulty: 0 }, {id: "mt-b3-0b", difficulty: 0 }, { id: "mt-b3-1a", difficulty: 1 }]
 * ... and so on.
 * There should be buckets for B1, B2, B3, C1, C2, C3, C4, C5 - so 8 buckets in total
 * Then assign exercises for each week starting with the easiest exercise in each bucket and working up
 * Assign exercises in the following order:
 * Off Season: B1, B2, B3, C5, repeating until end date is reached
 * Prep: C1, C2, C3, C5, repeating until end date is reached
 * Pre-Comp: C4, C5, repeating until end date is reached
 *
 * ONLY ASSIGN 1 EXERCISE PER WEEK FOR MENTAL TOUGHNESS
 * DO NOT ASSIGN EXERCISES DURING THE COMPETITION PERIOD
 *
 * Schedule mental wellness:
 * First, group all exercises into skill buckets sorted by difficulty and then alphabetically.
 * Mental wellness will have the following skills: M, P1, P2, P3, E1, E2, R, A, L1, L2, L3, U, S
 * For example:
 * M_bucket: [{ id: "mw-m-0a", difficulty: 0 }, {id: "mw-m-0b", difficulty: 0 }, { id: "mw-m-1a", difficulty: 1 }]
 * P1_bucket: [{ id: "mw-p1-0a", difficulty: 0 }, {id: "mw-p1-0b", difficulty: 0 }, { id: "mw-p1-1a", difficulty: 1 }]
 * ... and so on.
 *
 * There should be buckets for M, P1, P2, P3, E1, E2, R, A, L1, L2, L3, U, S - so 13 buckets in total
 * Then assign exercises for each week starting with the easiest exercise in each bucket and working up
 *
 * Assign exercises in the following order:
 * M, [P1, P2, P3], [E1, E2], R, M, A, [P1, P2, P3], [L1, L2, L3], U, S
 * In the above, the brackets indicate that one exercise from that bucket should be assigned per week.
 * Assign the exercises in the order they appear in the array. For example:
 * M, P1, E1, R, M, A, P2, L1, U, S, M, P3, E2, R, M, A, P1, L2, U, S...
 *
 * ONLY ASSIGN 1 EXERCISE PER WEEK FOR MENTAL WELLNESS
 * DO NOT ASSIGN EXERCISES DURING THE COMPETITION PERIOD
 */

// Helper types for exercise parsing
type ParsedExercise = {
  id: string;
  name: string;
  type: "mt" | "mw";
  skill: string;
  difficulty: number;
  identifier: string;
};

type ExerciseBucket = {
  [skill: string]: ParsedExercise[];
};

// Helper function to parse exercise ID
const parseExerciseId = (exercise: Exercise): ParsedExercise | null => {
  const parts = exercise.id.toLowerCase().split("-");
  if (parts.length !== 3) return null;

  const [type, skill, difficultyAndId] = parts;
  if (type !== "mt" && type !== "mw") return null;

  // Extract difficulty (number) and identifier (letter)
  const match = difficultyAndId.match(/^(\d+)([a-z]+)$/);
  if (!match) return null;

  const difficulty = parseInt(match[1]);
  const identifier = match[2];

  return {
    id: exercise.id,
    name: exercise.name,
    type: type as "mt" | "mw",
    skill: skill.toUpperCase(),
    difficulty,
    identifier,
  };
};

// Helper function to validate date ranges
const validateDateRanges = (
  offSeasonStart: Date,
  offSeasonEnd: Date,
  prepStart: Date,
  prepEnd: Date,
  preCompStart: Date,
  preCompEnd: Date
): string[] => {
  const errors: string[] = [];

  const offSeasonWeeks = Math.ceil(
    (offSeasonEnd.getTime() - offSeasonStart.getTime()) /
      (7 * 24 * 60 * 60 * 1000)
  );
  const prepWeeks = Math.ceil(
    (prepEnd.getTime() - prepStart.getTime()) / (7 * 24 * 60 * 60 * 1000)
  );
  const preCompWeeks = Math.ceil(
    (preCompEnd.getTime() - preCompStart.getTime()) / (7 * 24 * 60 * 60 * 1000)
  );

  if (offSeasonWeeks < 4) {
    errors.push("Off season must be at least 4 weeks");
  }
  if (prepWeeks < 4) {
    errors.push("Prep season must be at least 4 weeks");
  }
  if (preCompWeeks < 2) {
    errors.push("Pre-comp season must be at least 2 weeks");
  }

  return errors;
};

// Helper function to create exercise buckets
const createExerciseBuckets = (exercises: ParsedExercise[]): ExerciseBucket => {
  const buckets: ExerciseBucket = {};

  exercises.forEach((exercise) => {
    if (!buckets[exercise.skill]) {
      buckets[exercise.skill] = [];
    }
    buckets[exercise.skill].push(exercise);
  });

  // Sort each bucket by difficulty, then alphabetically by identifier
  Object.keys(buckets).forEach((skill) => {
    buckets[skill].sort((a, b) => {
      if (a.difficulty !== b.difficulty) {
        return a.difficulty - b.difficulty;
      }
      return a.identifier.localeCompare(b.identifier);
    });
  });

  return buckets;
};

// Helper function to create interleaved MW buckets
const createInterleavedMWBuckets = (
  exercises: ParsedExercise[]
): ExerciseBucket => {
  const initialBuckets = createExerciseBuckets(exercises);
  const interleavedBuckets: ExerciseBucket = {};

  // Group skills by their base letter (P1, P2, P3 -> P)
  const skillGroups: { [baseLetter: string]: string[] } = {};

  Object.keys(initialBuckets).forEach((skill) => {
    const baseLetter = skill.charAt(0); // Get first letter (P from P1, P2, P3)
    if (!skillGroups[baseLetter]) {
      skillGroups[baseLetter] = [];
    }
    skillGroups[baseLetter].push(skill);
  });

  // For each base letter, interleave the variants
  Object.keys(skillGroups).forEach((baseLetter) => {
    const variants = skillGroups[baseLetter].sort(); // Sort variants (P1, P2, P3)

    if (variants.length === 1) {
      // Single variant, just copy the bucket
      interleavedBuckets[baseLetter] = [...initialBuckets[variants[0]]];
    } else {
      // Multiple variants, interleave them
      interleavedBuckets[baseLetter] = [];

      // Find the maximum length among all variant buckets
      const maxLength = Math.max(
        ...variants.map((v) => initialBuckets[v].length)
      );

      // Interleave exercises from each variant
      for (let i = 0; i < maxLength; i++) {
        variants.forEach((variant) => {
          if (i < initialBuckets[variant].length) {
            interleavedBuckets[baseLetter].push(initialBuckets[variant][i]);
          }
        });
      }
    }
  });

  return interleavedBuckets;
};

// Helper function to get next exercise from bucket
const getNextExercise = (
  bucket: ParsedExercise[],
  usedIndices: Set<number>
): ParsedExercise | null => {
  for (let i = 0; i < bucket.length; i++) {
    if (!usedIndices.has(i)) {
      usedIndices.add(i);
      return bucket[i];
    }
  }
  return null;
};

// Helper function to add weeks to a date
const addWeeks = (date: Date, weeks: number): Date => {
  const result = new Date(date);
  result.setDate(result.getDate() + weeks * 7);
  return result;
};

export const generatePeriodization = (
  offSeasonStartDate: Date,
  offSeasonEndDate: Date,
  prepStartDate: Date,
  prepEndDate: Date,
  preCompStartDate: Date,
  preCompEndDate: Date,
  _compStartDate: Date, // Not used in algorithm - no exercises assigned during competition
  _compEndDate: Date, // Not used in algorithm - no exercises assigned during competition
  allExercises: Exercise[],
  scheduleMentalToughness: boolean,
  scheduleMentalWellness: boolean,
  mentalWellnessStartDate?: Date,
  mentalWellnessEndDate?: Date
): { entries: PeriodizationEntry[]; errors: string[] } => {
  const entries: PeriodizationEntry[] = [];
  const errors: string[] = [];

  // Validate date ranges
  const validationErrors = validateDateRanges(
    offSeasonStartDate,
    offSeasonEndDate,
    prepStartDate,
    prepEndDate,
    preCompStartDate,
    preCompEndDate
  );
  errors.push(...validationErrors);

  if (errors.length > 0) {
    return { entries, errors };
  }

  // Parse all exercises
  const parsedExercises = allExercises
    .map(parseExerciseId)
    .filter((exercise): exercise is ParsedExercise => exercise !== null);

  // Separate MT and MW exercises
  const mtExercises = parsedExercises.filter((e) => e.type === "mt");
  const mwExercises = parsedExercises.filter((e) => e.type === "mw");

  // Create buckets
  const mtBuckets = createExerciseBuckets(mtExercises);
  const mwBuckets = createInterleavedMWBuckets(mwExercises);

  // Track used indices for each bucket
  const mtUsedIndices: { [skill: string]: Set<number> } = {};
  Object.keys(mtBuckets).forEach((skill) => {
    mtUsedIndices[skill] = new Set();
  });

  const mwUsedIndices: { [skill: string]: Set<number> } = {};
  Object.keys(mwBuckets).forEach((skill) => {
    mwUsedIndices[skill] = new Set();
  });

  // Helper function to schedule exercises for a period
  const scheduleForPeriod = (
    startDate: Date,
    endDate: Date,
    mtPattern: string[],
    mwPattern: string[],
    startWeek: number
  ): number => {
    let currentDate = new Date(startDate);
    let weekCounter = startWeek;
    let mtPatternIndex = 0;
    let mwPatternIndex = 0;

    while (currentDate <= endDate) {
      const dateStr = currentDate.toISOString().split("T")[0];

      // Schedule Mental Toughness exercise
      if (scheduleMentalToughness && mtPattern.length > 0) {
        const skill = mtPattern[mtPatternIndex % mtPattern.length];
        if (mtBuckets[skill]) {
          const exercise = getNextExercise(
            mtBuckets[skill],
            mtUsedIndices[skill]
          );
          if (exercise) {
            entries.push({
              week: weekCounter,
              assignmentDate: dateStr,
              exerciseType: "MT exercise",
              exerciseName: exercise.name,
              exerciseId: exercise.id,
            });
          }
        }
        mtPatternIndex++;
      }

      // Schedule Mental Wellness exercise (only within specified date range)
      if (scheduleMentalWellness && mwPattern.length > 0) {
        const shouldScheduleMW =
          mentalWellnessStartDate && mentalWellnessEndDate
            ? currentDate >= mentalWellnessStartDate &&
              currentDate <= mentalWellnessEndDate
            : true; // Default behavior if no custom range specified

        if (shouldScheduleMW) {
          const skill = mwPattern[mwPatternIndex % mwPattern.length];
          if (mwBuckets[skill]) {
            const exercise = getNextExercise(
              mwBuckets[skill],
              mwUsedIndices[skill]
            );
            if (exercise) {
              entries.push({
                week: weekCounter,
                assignmentDate: dateStr,
                exerciseType: "MW exercise",
                exerciseName: exercise.name,
                exerciseId: exercise.id,
              });
            }
          }
        }
        mwPatternIndex++;
      }

      currentDate = addWeeks(currentDate, 1);
      weekCounter++;
    }

    return weekCounter;
  };

  // MW Pattern: M, P, E, R, M, A, P, L, U, S (simplified pattern)
  const mwPattern = ["M", "P", "E", "R", "M", "A", "P", "L", "U", "S"];

  let currentWeek = 1;

  // Off Season: MT (B1, B2, B3, C5), MW (full pattern)
  const offSeasonMTPattern = ["B1", "B2", "B3", "C5"];
  currentWeek = scheduleForPeriod(
    offSeasonStartDate,
    offSeasonEndDate,
    offSeasonMTPattern,
    mwPattern,
    currentWeek
  );

  // Prep: MT (C1, C2, C3, C5), MW (full pattern)
  const prepMTPattern = ["C1", "C2", "C3", "C5"];
  currentWeek = scheduleForPeriod(
    prepStartDate,
    prepEndDate,
    prepMTPattern,
    mwPattern,
    currentWeek
  );

  // Pre-Comp: MT (C4, C5), MW (full pattern)
  const preCompMTPattern = ["C4", "C5"];
  scheduleForPeriod(
    preCompStartDate,
    preCompEndDate,
    preCompMTPattern,
    mwPattern,
    currentWeek
  );

  // Sort entries by assignment date (but keep original week numbers)
  entries.sort(
    (a, b) =>
      new Date(a.assignmentDate).getTime() -
      new Date(b.assignmentDate).getTime()
  );

  return { entries, errors };
};
