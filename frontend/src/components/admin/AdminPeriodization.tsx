import { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { PageHeader } from "../ui/PageHeader";
import { Card, CardHeader } from "../ui/Card";
import { Button } from "../ui/Button";
import { DataTable } from "../ui/DataTable";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { ErrorMessage } from "../ui/ErrorMessage";
import { Column } from "../../types/components.types";
import { useAdmin } from "../../context/AdminContext";
import {
  Calendar,
  Play,
  BookOpen,
  Eye,
  BicepsFlexed,
  HeartPlus,
} from "lucide-react";
import {
  generatePeriodization,
  PeriodizationEntry,
} from "../../utils/periodization";
import { Label } from "../ui/Label";
import { PeriodizationBar } from "../ui/PeriodizationBar";
import { DateRangeBar } from "../ui/DateRangeBar";

const AdminPeriodization = () => {
  const {
    allExercises,
    allVisualizations,
    loading,
    error,
    clearError,
    fetchAllExercises,
    fetchAllVisualizations,
  } = useAdmin();
  // Simplified season dates - only need start and end
  const [offSeasonStartDate, setOffSeasonStartDate] = useState<string>("");
  const [competitionEndDate, setCompetitionEndDate] = useState<string>("");

  // Calculated phase durations from the draggable bar
  const [phaseDurations, setPhaseDurations] = useState({
    offSeasonWeeks: 0,
    prepWeeks: 0,
    preCompWeeks: 0,
    competitionWeeks: 0,
  });

  // Mental wellness date range (in weeks from start)
  const [mentalWellnessRange, setMentalWellnessRange] = useState({
    startWeek: 1,
    endWeek: 1,
  });

  // Scheduling options
  const [scheduleMentalToughness, setScheduleMentalToughness] =
    useState<boolean>(true);
  const [scheduleMentalWellness, setScheduleMentalWellness] =
    useState<boolean>(true);

  const [periodizationData, setPeriodizationData] = useState<
    PeriodizationEntry[]
  >([]);
  const [errors, setErrors] = useState<string[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  useEffect(() => {
    fetchAllExercises();
    fetchAllVisualizations();
  }, [fetchAllExercises, fetchAllVisualizations]);

  // Initialize mental wellness range when total weeks change
  useEffect(() => {
    if (offSeasonStartDate && competitionEndDate) {
      const start = new Date(offSeasonStartDate);
      const end = new Date(competitionEndDate);
      const totalWeeks = Math.ceil(
        (end.getTime() - start.getTime()) / (7 * 24 * 60 * 60 * 1000)
      );

      if (totalWeeks > 0) {
        setMentalWellnessRange({
          startWeek: 1,
          endWeek: totalWeeks,
        });
      }
    }
  }, [offSeasonStartDate, competitionEndDate]);

  // Calculate individual phase dates based on the phase durations
  const calculatePhaseDates = () => {
    if (!offSeasonStartDate || !competitionEndDate) {
      return null;
    }

    const startDate = new Date(offSeasonStartDate);

    // Calculate end dates for each phase
    const offSeasonEndDate = new Date(startDate);
    offSeasonEndDate.setDate(
      startDate.getDate() + phaseDurations.offSeasonWeeks * 7 - 1
    );

    const prepStartDate = new Date(offSeasonEndDate);
    prepStartDate.setDate(offSeasonEndDate.getDate() + 1);
    const prepEndDate = new Date(prepStartDate);
    prepEndDate.setDate(
      prepStartDate.getDate() + phaseDurations.prepWeeks * 7 - 1
    );

    const preCompStartDate = new Date(prepEndDate);
    preCompStartDate.setDate(prepEndDate.getDate() + 1);
    const preCompEndDate = new Date(preCompStartDate);
    preCompEndDate.setDate(
      preCompStartDate.getDate() + phaseDurations.preCompWeeks * 7 - 1
    );

    const compStartDate = new Date(preCompEndDate);
    compStartDate.setDate(preCompEndDate.getDate() + 1);
    const compEndDate = new Date(competitionEndDate);

    // Calculate mental wellness dates
    const mentalWellnessStartDate = new Date(startDate);
    mentalWellnessStartDate.setDate(
      startDate.getDate() + (mentalWellnessRange.startWeek - 1) * 7
    );

    const mentalWellnessEndDate = new Date(startDate);
    mentalWellnessEndDate.setDate(
      startDate.getDate() + (mentalWellnessRange.endWeek - 1) * 7 + 6
    );

    return {
      offSeasonStart: startDate,
      offSeasonEnd: offSeasonEndDate,
      prepStart: prepStartDate,
      prepEnd: prepEndDate,
      preCompStart: preCompStartDate,
      preCompEnd: preCompEndDate,
      compStart: compStartDate,
      compEnd: compEndDate,
      mentalWellnessStart: mentalWellnessStartDate,
      mentalWellnessEnd: mentalWellnessEndDate,
    };
  };

  const handleGeneratePeriodization = async () => {
    if (!offSeasonStartDate || !competitionEndDate) {
      return;
    }

    const phaseDates = calculatePhaseDates();
    if (!phaseDates) {
      return;
    }

    setIsGenerating(true);
    setErrors([]);

    const result = generatePeriodization(
      phaseDates.offSeasonStart,
      phaseDates.offSeasonEnd,
      phaseDates.prepStart,
      phaseDates.prepEnd,
      phaseDates.preCompStart,
      phaseDates.preCompEnd,
      phaseDates.compStart,
      phaseDates.compEnd,
      allExercises,
      scheduleMentalToughness,
      scheduleMentalWellness,
      phaseDates.mentalWellnessStart,
      phaseDates.mentalWellnessEnd
    );

    setPeriodizationData(result.entries);
    setErrors(result.errors);
    setIsGenerating(false);
  };

  const columns: Column<PeriodizationEntry>[] = [
    {
      header: "Week",
      accessorKey: "week",
      cell: (entry) => (
        <div className="flex items-center">
          <Calendar className="h-4 w-4 text-blue-600 mr-2" />
          <span className="font-medium">Week {entry.week}</span>
        </div>
      ),
    },
    {
      header: "Assignment Date",
      accessorKey: "assignmentDate",
      cell: (entry) => (
        <span className="text-sm text-gray-900">
          {new Date(entry.assignmentDate).toLocaleDateString("en-US", {
            weekday: "short",
            year: "numeric",
            month: "short",
            day: "numeric",
          })}
        </span>
      ),
    },
    {
      header: "Type",
      accessorKey: "exerciseType",
      cell: (entry) => (
        <div className="flex items-center">
          {entry.exerciseType === "MT exercise" ? (
            <Label icon={BicepsFlexed} color="orange" text="MT Exercise" />
          ) : entry.exerciseType === "MW exercise" ? (
            <Label icon={HeartPlus} color="green" text="MW Exercise" />
          ) : (
            <Label icon={Eye} color="purple" text="Visualization" />
          )}
        </div>
      ),
    },
    {
      header: "Exercise/Visualization",
      accessorKey: "exerciseName",
      cell: (entry) => (
        <div>
          <Link
            to={`/dashboard/exercises/${entry.exerciseId}`}
            target="_blank"
            className="text-sm font-medium text-blue-700 hover:underline cursor-pointer"
            title={`View details for ${entry.exerciseName}`}
            rel="noopener noreferrer"
          >
            {entry.exerciseName}
          </Link>
          <p className="text-xs text-gray-500">ID: {entry.exerciseId}</p>
        </div>
      ),
    },
  ];

  if (loading.exercises || loading.visualizations) {
    return <LoadingSpinner />;
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Periodization Testing"
        description="Test the periodization algorithm by generating assignment schedules"
      />

      {error && <ErrorMessage message={error} onDismiss={clearError} />}

      {/* Configuration Form */}
      <Card>
        <CardHeader title="Periodization Configuration" />
        <div className="p-6 pt-0">
          {/* Season Dates */}
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Season Dates
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <label
                    htmlFor="offSeasonStartDate"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Off Season Start Date
                  </label>
                  <input
                    type="date"
                    id="offSeasonStartDate"
                    value={offSeasonStartDate}
                    onChange={(e) => setOffSeasonStartDate(e.target.value)}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  />
                </div>

                <div>
                  <label
                    htmlFor="competitionEndDate"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Competition End Date
                  </label>
                  <input
                    type="date"
                    id="competitionEndDate"
                    value={competitionEndDate}
                    onChange={(e) => setCompetitionEndDate(e.target.value)}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  />
                </div>
              </div>

              {/* Periodization Bar */}
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-3">
                  Phase Duration Adjustment
                </h4>
                <p className="text-sm text-gray-600 mb-4">
                  Drag the dividers to adjust the duration of each phase. Each
                  phase has minimum requirements.
                </p>
                <PeriodizationBar
                  offSeasonStart={offSeasonStartDate}
                  competitionEnd={competitionEndDate}
                  onPhasesChange={setPhaseDurations}
                />
              </div>

              {/* Mental Wellness Date Range */}
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-3">
                  Mental Wellness Schedule Range
                </h4>
                <p className="text-sm text-gray-600 mb-4">
                  Drag the start and end handles to specify when mental wellness
                  exercises should be scheduled.
                </p>
                <DateRangeBar
                  totalStartDate={offSeasonStartDate}
                  totalEndDate={competitionEndDate}
                  rangeStartWeek={mentalWellnessRange.startWeek}
                  rangeEndWeek={mentalWellnessRange.endWeek}
                  onRangeChange={(startWeek, endWeek) =>
                    setMentalWellnessRange({ startWeek, endWeek })
                  }
                  title="Mental Wellness Range"
                  color="bg-purple-500"
                />
              </div>
            </div>

            {/* Scheduling Options */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Scheduling Options
              </h3>
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    id="scheduleMentalToughness"
                    type="checkbox"
                    checked={scheduleMentalToughness}
                    onChange={(e) =>
                      setScheduleMentalToughness(e.target.checked)
                    }
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label
                    htmlFor="scheduleMentalToughness"
                    className="ml-2 block text-sm text-gray-900"
                  >
                    Schedule Mental Toughness Exercises
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    id="scheduleMentalWellness"
                    type="checkbox"
                    checked={scheduleMentalWellness}
                    onChange={(e) =>
                      setScheduleMentalWellness(e.target.checked)
                    }
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label
                    htmlFor="scheduleMentalWellness"
                    className="ml-2 block text-sm text-gray-900"
                  >
                    Schedule Mental Wellness Exercises
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Error Display */}
          {errors.length > 0 && (
            <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-md">
              <h4 className="text-sm font-medium text-red-800 mb-2">
                Validation Errors:
              </h4>
              <ul className="text-sm text-red-700 space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}

          <div className="mt-6">
            <Button
              onClick={handleGeneratePeriodization}
              disabled={
                !offSeasonStartDate || !competitionEndDate || isGenerating
              }
              className="flex items-center"
            >
              <Play className="h-4 w-4 mr-2" />
              {isGenerating ? "Generating..." : "Generate Periodization"}
            </Button>
          </div>
        </div>
      </Card>

      {/* Available Resources Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-4">
          <div className="flex items-center">
            <BookOpen className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">
                Available Exercises
              </p>
              <p className="text-lg font-semibold text-gray-900">
                {allExercises.length}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center">
            <Eye className="h-8 w-8 text-purple-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">
                Available Visualizations
              </p>
              <p className="text-lg font-semibold text-gray-900">
                {allVisualizations.length}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Periodization Results */}
      {periodizationData.length > 0 && (
        <Card>
          <CardHeader
            title={`Periodization Schedule (${periodizationData.length} assignments)`}
          />
          <div className="p-6 pt-0">
            {/* Category summary split by Mental Toughness and Mental Wellness */}
            {(() => {
              // Skills for each type (from utils/periodization.ts)
              const mtSkills = ["B1", "B2", "B3", "C1", "C2", "C3", "C4", "C5"];
              const mwSkills = [
                "M",
                "P1",
                "P2",
                "P3",
                "E1",
                "E2",
                "R",
                "A",
                "L1",
                "L2",
                "L3",
                "U",
                "S",
              ];

              // Count per skill/category for each type
              const mtCounts: Record<string, number> = {};
              const mwCounts: Record<string, number> = {};

              periodizationData.forEach((entry) => {
                const id = entry.exerciseId?.toString().toLowerCase();
                let skill = "Other";
                if (id) {
                  const parts = id.split("-");
                  if (parts.length >= 2) {
                    skill = parts[1].toUpperCase();
                  }
                }
                if (entry.exerciseType === "MT exercise") {
                  mtCounts[skill] = (mtCounts[skill] || 0) + 1;
                } else if (entry.exerciseType === "MW exercise") {
                  mwCounts[skill] = (mwCounts[skill] || 0) + 1;
                }
              });

              return (
                <div className="mb-4">
                  <div className="mb-2 font-semibold text-orange-700 text-left">
                    Mental Toughness
                  </div>
                  <div className="flex flex-wrap gap-4 mb-4">
                    {mtSkills.map((skill) => (
                      <div
                        key={skill}
                        className="px-3 py-1 rounded bg-orange-100 text-sm font-medium text-orange-700"
                      >
                        {skill}:{" "}
                        <span className="font-bold">
                          {mtCounts[skill] || 0}
                        </span>
                      </div>
                    ))}
                  </div>
                  <div className="mb-2 font-semibold text-green-700 text-left">
                    Mental Wellness
                  </div>
                  <div className="flex flex-wrap gap-4">
                    {mwSkills.map((skill) => (
                      <div
                        key={skill}
                        className="px-3 py-1 rounded bg-green-100 text-sm font-medium text-green-700"
                      >
                        {skill}:{" "}
                        <span className="font-bold">
                          {mwCounts[skill] || 0}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              );
            })()}
            <DataTable
              columns={columns}
              data={periodizationData}
              className="w-full"
            />
          </div>
        </Card>
      )}

      {/* Empty State */}
      {periodizationData.length === 0 &&
        offSeasonStartDate &&
        competitionEndDate && (
          <Card className="p-8">
            <div className="text-center">
              <Calendar className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                No periodization generated
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                Click "Generate Periodization" to create an assignment schedule.
              </p>
            </div>
          </Card>
        )}
    </div>
  );
};

export default AdminPeriodization;
