import React, { useState, useRef, useEffect } from "react";

interface PeriodizationPhase {
  name: string;
  startWeek: number;
  endWeek: number;
  color: string;
  minWeeks: number;
}

interface InteractivePeriodizationBarProps {
  totalWeeks: number;
  onPhasesChange: (phases: {
    offSeasonWeeks: number;
    prepWeeks: number;
    preCompWeeks: number;
    competitionWeeks: number;
  }) => void;
}

export const InteractivePeriodizationBar: React.FC<
  InteractivePeriodizationBarProps
> = ({ totalWeeks, onPhasesChange }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState<{
    phaseIndex: number;
    handle: "start" | "end";
  } | null>(null);
  const [dragStartX, setDragStartX] = useState(0);
  const [dragStartPhases, setDragStartPhases] = useState<PeriodizationPhase[]>(
    []
  );

  // Initialize phases with default distribution but allow gaps
  const [phases, setPhases] = useState<PeriodizationPhase[]>([
    {
      name: "Off Season",
      startWeek: 1,
      endWeek: Math.max(4, Math.floor(totalWeeks * 0.4)),
      color: "bg-blue-500",
      minWeeks: 4,
    },
    {
      name: "Prep",
      startWeek: Math.max(4, Math.floor(totalWeeks * 0.4)) + 1,
      endWeek: Math.max(8, Math.floor(totalWeeks * 0.8)),
      color: "bg-green-500",
      minWeeks: 4,
    },
    {
      name: "Pre-Comp",
      startWeek: Math.max(8, Math.floor(totalWeeks * 0.8)) + 1,
      endWeek: Math.max(10, Math.floor(totalWeeks * 0.95)),
      color: "bg-yellow-500",
      minWeeks: 2,
    },
    {
      name: "Competition",
      startWeek: Math.max(10, Math.floor(totalWeeks * 0.95)) + 1,
      endWeek: totalWeeks,
      color: "bg-red-500",
      minWeeks: 1,
    },
  ]);

  // Update phases when total weeks changes
  useEffect(() => {
    if (totalWeeks > 0) {
      const newPhases = [
        {
          name: "Off Season",
          startWeek: 1,
          endWeek: Math.max(4, Math.floor(totalWeeks * 0.4)),
          color: "bg-blue-500",
          minWeeks: 4,
        },
        {
          name: "Prep",
          startWeek: Math.max(4, Math.floor(totalWeeks * 0.4)) + 1,
          endWeek: Math.max(8, Math.floor(totalWeeks * 0.8)),
          color: "bg-green-500",
          minWeeks: 4,
        },
        {
          name: "Pre-Comp",
          startWeek: Math.max(8, Math.floor(totalWeeks * 0.8)) + 1,
          endWeek: Math.max(10, Math.floor(totalWeeks * 0.95)),
          color: "bg-yellow-500",
          minWeeks: 2,
        },
        {
          name: "Competition",
          startWeek: Math.max(10, Math.floor(totalWeeks * 0.95)) + 1,
          endWeek: totalWeeks,
          color: "bg-red-500",
          minWeeks: 1,
        },
      ];

      setPhases(newPhases);
    }
  }, [totalWeeks]);

  // Notify parent of phase changes
  useEffect(() => {
    onPhasesChange({
      offSeasonWeeks:
        Math.max(0, phases[0]?.endWeek - phases[0]?.startWeek + 1) || 0,
      prepWeeks:
        Math.max(0, phases[1]?.endWeek - phases[1]?.startWeek + 1) || 0,
      preCompWeeks:
        Math.max(0, phases[2]?.endWeek - phases[2]?.startWeek + 1) || 0,
      competitionWeeks:
        Math.max(0, phases[3]?.endWeek - phases[3]?.startWeek + 1) || 0,
    });
  }, [phases, onPhasesChange]);

  const handleMouseDown = (
    e: React.MouseEvent,
    phaseIndex: number,
    handle: "start" | "end"
  ) => {
    e.preventDefault();
    setIsDragging({ phaseIndex, handle });
    setDragStartX(e.clientX);
    setDragStartPhases([...phases]);
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const containerWidth = rect.width;
    const deltaX = e.clientX - dragStartX;
    // Calculate pixels per week for smoother dragging
    const pixelsPerWeek = containerWidth / totalWeeks;
    const deltaWeeks = Math.round(deltaX / pixelsPerWeek);

    // Optional: debug output
    // console.log("DELTA WEEKS", deltaWeeks);

    const newPhases = [...dragStartPhases];
    const { phaseIndex, handle } = isDragging;

    if (handle === "start" && phaseIndex === 0) {
      // Use dragStartPhases as base, clamp, and only update if changed
      let newStartWeek = dragStartPhases[phaseIndex].startWeek + deltaWeeks;
      const minStartWeek = 1;
      const maxStartWeek =
        dragStartPhases[phaseIndex].endWeek -
        dragStartPhases[phaseIndex].minWeeks +
        1;
      newStartWeek = Math.max(
        minStartWeek,
        Math.min(maxStartWeek, newStartWeek)
      );
      if (newStartWeek !== phases[phaseIndex].startWeek) {
        newPhases[phaseIndex].startWeek = newStartWeek;
        setPhases(newPhases);
      }
    } else if (handle === "end") {
      // Use dragStartPhases as base, clamp, and only update if changed
      let newEndWeek = dragStartPhases[phaseIndex].endWeek + deltaWeeks;
      const minEndWeek =
        dragStartPhases[phaseIndex].startWeek +
        dragStartPhases[phaseIndex].minWeeks -
        1;
      const maxEndWeek = totalWeeks;
      newEndWeek = Math.max(minEndWeek, Math.min(maxEndWeek, newEndWeek));

      // If there's a next phase, check if it can maintain its minimum duration
      let canChange = true;
      if (phaseIndex < newPhases.length - 1) {
        const nextPhaseNewStart = newEndWeek + 1;
        const nextPhaseCurrentEnd = dragStartPhases[phaseIndex + 1].endWeek;
        const nextPhaseNewDuration =
          nextPhaseCurrentEnd - nextPhaseNewStart + 1;
        if (nextPhaseNewDuration < dragStartPhases[phaseIndex + 1].minWeeks) {
          canChange = false;
        }
      }

      if (canChange && newEndWeek !== phases[phaseIndex].endWeek) {
        newPhases[phaseIndex].endWeek = newEndWeek;
        // Update the start of the next phase
        if (phaseIndex < newPhases.length - 1) {
          newPhases[phaseIndex + 1].startWeek = newEndWeek + 1;
        }
        setPhases(newPhases);
      }
    }
  };

  const handleMouseUp = () => {
    setIsDragging(null);
    setDragStartX(0);
    setDragStartPhases([]);
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [isDragging, dragStartX, dragStartPhases, totalWeeks]);

  if (totalWeeks === 0) {
    return (
      <div className="text-center text-gray-500 py-8">
        Please select both off season start date and competition end date to see
        the periodization bar.
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="text-sm text-gray-600">
        Total Duration: {totalWeeks} weeks (drag handles to adjust in 1-week
        increments)
      </div>

      <div
        ref={containerRef}
        className="relative h-16 bg-gray-200 rounded-lg overflow-hidden cursor-pointer select-none"
      >
        {phases.map((phase, index) => {
          const startPercentage = ((phase.startWeek - 1) / totalWeeks) * 100;
          const widthPercentage =
            ((phase.endWeek - phase.startWeek + 1) / totalWeeks) * 100;

          return (
            <React.Fragment key={phase.name}>
              <div
                className={`absolute top-0 h-full ${phase.color} flex items-center justify-center text-white font-medium text-sm transition-all duration-200`}
                style={{
                  left: `${startPercentage}%`,
                  width: `${widthPercentage}%`,
                }}
              >
                <div className="text-center">
                  <div>{phase.name}</div>
                  <div className="text-xs opacity-90">
                    W{phase.startWeek}-{phase.endWeek} (
                    {phase.endWeek - phase.startWeek + 1}w)
                  </div>
                </div>
              </div>

              {/* Start handle - only for the first phase (Off Season) */}
              {index === 0 && (
                <div
                  className="absolute top-0 h-full w-2 bg-white border-2 border-gray-300 cursor-col-resize hover:bg-gray-100 transition-colors duration-200 z-10"
                  style={{
                    left: `calc(${startPercentage}% - 4px)`,
                  }}
                  onMouseDown={(e) => handleMouseDown(e, index, "start")}
                  title={`Drag to adjust ${phase.name} start`}
                />
              )}

              {/* End handle - for all phases */}
              <div
                className="absolute top-0 h-full w-2 bg-white border-2 border-gray-300 cursor-col-resize hover:bg-gray-100 transition-colors duration-200 z-10"
                style={{
                  left: `calc(${startPercentage + widthPercentage}% - 4px)`,
                }}
                onMouseDown={(e) => handleMouseDown(e, index, "end")}
                title={`Drag to adjust ${phase.name} end${
                  index < phases.length - 1
                    ? ` / ${phases[index + 1].name} start`
                    : ""
                }`}
              />
            </React.Fragment>
          );
        })}
      </div>

      {/* Phase details */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        {phases.map((phase) => (
          <div key={phase.name} className="flex items-center space-x-2">
            <div className={`w-4 h-4 ${phase.color} rounded`}></div>
            <span className="font-medium">{phase.name}:</span>
            <span>
              W{phase.startWeek}-{phase.endWeek}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};
