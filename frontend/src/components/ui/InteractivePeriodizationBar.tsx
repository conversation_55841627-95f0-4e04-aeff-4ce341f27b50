import React, { useState, useRef, useEffect } from "react";

interface PeriodizationPhase {
  name: string;
  startWeek: number;
  endWeek: number;
  color: string;
  minWeeks: number;
}

interface InteractivePeriodizationBarProps {
  totalWeeks: number;
  onPhasesChange: (phases: {
    offSeasonWeeks: number;
    prepWeeks: number;
    preCompWeeks: number;
    competitionWeeks: number;
  }) => void;
}

export const InteractivePeriodizationBar: React.FC<
  InteractivePeriodizationBarProps
> = ({ totalWeeks, onPhasesChange }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState<{
    phaseIndex: number;
    handle: "start" | "end";
  } | null>(null);
  const [dragStartX, setDragStartX] = useState(0);
  const [dragStartPhases, setDragStartPhases] = useState<PeriodizationPhase[]>(
    []
  );

  // Initialize phases with default distribution but allow gaps
  const [phases, setPhases] = useState<PeriodizationPhase[]>([
    {
      name: "Off Season",
      startWeek: 1,
      endWeek: Math.max(4, Math.floor(totalWeeks * 0.4)),
      color: "bg-blue-500",
      minWeeks: 4,
    },
    {
      name: "Prep",
      startWeek: Math.max(4, Math.floor(totalWeeks * 0.4)) + 1,
      endWeek: Math.max(8, Math.floor(totalWeeks * 0.8)),
      color: "bg-green-500",
      minWeeks: 4,
    },
    {
      name: "Pre-Comp",
      startWeek: Math.max(8, Math.floor(totalWeeks * 0.8)) + 1,
      endWeek: Math.max(10, Math.floor(totalWeeks * 0.95)),
      color: "bg-yellow-500",
      minWeeks: 2,
    },
    {
      name: "Competition",
      startWeek: Math.max(10, Math.floor(totalWeeks * 0.95)) + 1,
      endWeek: totalWeeks,
      color: "bg-red-500",
      minWeeks: 1,
    },
  ]);

  // Update phases when total weeks changes
  useEffect(() => {
    if (totalWeeks > 0) {
      const newPhases = [
        {
          name: "Off Season",
          startWeek: 1,
          endWeek: Math.max(4, Math.floor(totalWeeks * 0.4)),
          color: "bg-blue-500",
          minWeeks: 4,
        },
        {
          name: "Prep",
          startWeek: Math.max(4, Math.floor(totalWeeks * 0.4)) + 1,
          endWeek: Math.max(8, Math.floor(totalWeeks * 0.8)),
          color: "bg-green-500",
          minWeeks: 4,
        },
        {
          name: "Pre-Comp",
          startWeek: Math.max(8, Math.floor(totalWeeks * 0.8)) + 1,
          endWeek: Math.max(10, Math.floor(totalWeeks * 0.95)),
          color: "bg-yellow-500",
          minWeeks: 2,
        },
        {
          name: "Competition",
          startWeek: Math.max(10, Math.floor(totalWeeks * 0.95)) + 1,
          endWeek: totalWeeks,
          color: "bg-red-500",
          minWeeks: 1,
        },
      ];

      setPhases(newPhases);
    }
  }, [totalWeeks]);

  // Notify parent of phase changes
  useEffect(() => {
    onPhasesChange({
      offSeasonWeeks:
        Math.max(0, phases[0]?.endWeek - phases[0]?.startWeek + 1) || 0,
      prepWeeks:
        Math.max(0, phases[1]?.endWeek - phases[1]?.startWeek + 1) || 0,
      preCompWeeks:
        Math.max(0, phases[2]?.endWeek - phases[2]?.startWeek + 1) || 0,
      competitionWeeks:
        Math.max(0, phases[3]?.endWeek - phases[3]?.startWeek + 1) || 0,
    });
  }, [phases, onPhasesChange]);

  const handleMouseDown = (
    e: React.MouseEvent,
    phaseIndex: number,
    handle: "start" | "end"
  ) => {
    e.preventDefault();
    setIsDragging({ phaseIndex, handle });
    setDragStartX(e.clientX);
    setDragStartPhases([...phases]);
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const containerWidth = rect.width;
    const deltaX = e.clientX - dragStartX;
    // Calculate delta weeks with finer precision - use smaller increments
    const pixelsPerWeek = containerWidth / totalWeeks;
    const deltaWeeks = Math.round(deltaX / pixelsPerWeek);

    const newPhases = [...dragStartPhases];
    const { phaseIndex, handle } = isDragging;

    if (handle === "start") {
      // Only allow moving the start of the first phase (Off Season)
      if (phaseIndex === 0) {
        const newStartWeek = Math.max(
          1,
          dragStartPhases[phaseIndex].startWeek + deltaWeeks
        );
        // Ensure minimum duration is maintained
        const maxStartWeek =
          dragStartPhases[phaseIndex].endWeek -
          dragStartPhases[phaseIndex].minWeeks +
          1;
        newPhases[phaseIndex].startWeek = Math.min(newStartWeek, maxStartWeek);
      }
    } else {
      // Handle end of phase - this affects the start of the next phase
      const proposedEndWeek = dragStartPhases[phaseIndex].endWeek + deltaWeeks;

      // Ensure minimum duration for current phase
      const minEndWeek =
        dragStartPhases[phaseIndex].startWeek +
        dragStartPhases[phaseIndex].minWeeks -
        1;

      // Ensure we don't exceed total weeks
      const maxEndWeek = totalWeeks;

      // If there's a next phase, ensure it can maintain its minimum duration
      let effectiveMaxEndWeek = maxEndWeek;
      if (phaseIndex < newPhases.length - 1) {
        // Calculate how much space we need for all remaining phases
        let remainingMinWeeks = 0;
        for (let i = phaseIndex + 1; i < newPhases.length; i++) {
          remainingMinWeeks += dragStartPhases[i].minWeeks;
        }
        effectiveMaxEndWeek = totalWeeks - remainingMinWeeks;
      }

      const newEndWeek = Math.max(
        minEndWeek,
        Math.min(effectiveMaxEndWeek, proposedEndWeek)
      );
      newPhases[phaseIndex].endWeek = newEndWeek;

      // Update subsequent phases
      if (phaseIndex < newPhases.length - 1) {
        newPhases[phaseIndex + 1].startWeek = newEndWeek + 1;

        // Ensure the next phase maintains its minimum duration
        const nextPhaseMinEnd =
          newPhases[phaseIndex + 1].startWeek +
          newPhases[phaseIndex + 1].minWeeks -
          1;
        if (newPhases[phaseIndex + 1].endWeek < nextPhaseMinEnd) {
          newPhases[phaseIndex + 1].endWeek = nextPhaseMinEnd;
        }

        // Cascade the changes to subsequent phases
        for (let i = phaseIndex + 2; i < newPhases.length; i++) {
          newPhases[i].startWeek = newPhases[i - 1].endWeek + 1;
          const minEnd = newPhases[i].startWeek + newPhases[i].minWeeks - 1;
          if (newPhases[i].endWeek < minEnd) {
            newPhases[i].endWeek = minEnd;
          }
        }
      }
    }

    setPhases(newPhases);
  };

  const handleMouseUp = () => {
    setIsDragging(null);
    setDragStartX(0);
    setDragStartPhases([]);
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [isDragging, dragStartX, dragStartPhases, totalWeeks]);

  if (totalWeeks === 0) {
    return (
      <div className="text-center text-gray-500 py-8">
        Please select both off season start date and competition end date to see
        the periodization bar.
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="text-sm text-gray-600">
        Total Duration: {totalWeeks} weeks (drag handles to adjust in 1-week
        increments)
      </div>

      <div
        ref={containerRef}
        className="relative h-16 bg-gray-200 rounded-lg overflow-hidden cursor-pointer select-none"
      >
        {phases.map((phase, index) => {
          const startPercentage = ((phase.startWeek - 1) / totalWeeks) * 100;
          const widthPercentage =
            ((phase.endWeek - phase.startWeek + 1) / totalWeeks) * 100;

          return (
            <React.Fragment key={phase.name}>
              <div
                className={`absolute top-0 h-full ${phase.color} flex items-center justify-center text-white font-medium text-sm transition-all duration-200`}
                style={{
                  left: `${startPercentage}%`,
                  width: `${widthPercentage}%`,
                }}
              >
                <div className="text-center">
                  <div>{phase.name}</div>
                  <div className="text-xs opacity-90">
                    W{phase.startWeek}-{phase.endWeek} (
                    {phase.endWeek - phase.startWeek + 1}w)
                  </div>
                </div>
              </div>

              {/* Start handle - only for the first phase (Off Season) */}
              {index === 0 && (
                <div
                  className="absolute top-0 h-full w-2 bg-white border-2 border-gray-300 cursor-col-resize hover:bg-gray-100 transition-colors duration-200 z-10"
                  style={{
                    left: `calc(${startPercentage}% - 4px)`,
                  }}
                  onMouseDown={(e) => handleMouseDown(e, index, "start")}
                  title={`Drag to adjust ${phase.name} start`}
                />
              )}

              {/* End handle - for all phases */}
              <div
                className="absolute top-0 h-full w-2 bg-white border-2 border-gray-300 cursor-col-resize hover:bg-gray-100 transition-colors duration-200 z-10"
                style={{
                  left: `calc(${startPercentage + widthPercentage}% - 4px)`,
                }}
                onMouseDown={(e) => handleMouseDown(e, index, "end")}
                title={`Drag to adjust ${phase.name} end${
                  index < phases.length - 1
                    ? ` / ${phases[index + 1].name} start`
                    : ""
                }`}
              />
            </React.Fragment>
          );
        })}
      </div>

      {/* Phase details */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        {phases.map((phase) => (
          <div key={phase.name} className="flex items-center space-x-2">
            <div className={`w-4 h-4 ${phase.color} rounded`}></div>
            <span className="font-medium">{phase.name}:</span>
            <span>
              W{phase.startWeek}-{phase.endWeek}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};
