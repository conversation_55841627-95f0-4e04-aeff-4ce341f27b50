import React from "react";

interface PeriodBreakdownBarProps {
  offSeasonWeeks: number;
  prepWeeks: number;
  preCompWeeks: number;
  competitionWeeks: number;
  totalWeeks: number;
}

export const PeriodBreakdownBar: React.FC<PeriodBreakdownBarProps> = ({
  offSeasonWeeks,
  prepWeeks,
  preCompWeeks,
  competitionWeeks,
  totalWeeks,
}) => {
  if (totalWeeks === 0) {
    return (
      <div className="text-center text-gray-500 py-8">
        Please fill in all periodization dates to see the breakdown.
      </div>
    );
  }

  const phases = [
    { name: "Off Season", weeks: offSeasonWeeks, color: "bg-blue-500" },
    { name: "Prep", weeks: prepWeeks, color: "bg-green-500" },
    { name: "Pre-Comp", weeks: preCompWeeks, color: "bg-yellow-500" },
    { name: "Competition", weeks: competitionWeeks, color: "bg-red-500" },
  ];

  return (
    <div className="space-y-4">
      <div className="text-sm text-gray-600">
        Period Breakdown: {totalWeeks} weeks total
      </div>
      
      <div className="relative h-16 bg-gray-200 rounded-lg overflow-hidden">
        {phases.map((phase, index) => {
          const widthPercentage = (phase.weeks / totalWeeks) * 100;
          const leftPercentage = phases.slice(0, index).reduce((sum, p) => sum + (p.weeks / totalWeeks) * 100, 0);
          
          return (
            <div
              key={phase.name}
              className={`absolute top-0 h-full ${phase.color} flex items-center justify-center text-white font-medium text-sm`}
              style={{
                left: `${leftPercentage}%`,
                width: `${widthPercentage}%`,
              }}
            >
              <div className="text-center">
                <div>{phase.name}</div>
                <div className="text-xs opacity-90">{phase.weeks}w</div>
              </div>
            </div>
          );
        })}
      </div>
      
      {/* Phase details */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        {phases.map((phase) => (
          <div key={phase.name} className="flex items-center space-x-2">
            <div className={`w-4 h-4 ${phase.color} rounded`}></div>
            <span className="font-medium">{phase.name}:</span>
            <span>{phase.weeks} weeks</span>
          </div>
        ))}
      </div>
    </div>
  );
};
